package com.dc.summer.service.transfer.strategy;

import com.dc.summer.service.transfer.WebDataTransferHelper;
import com.dc.summer.service.transfer.request.ExportDataRequest;

/**
 * 导出数据源处理策略接口
 * 使用策略模式处理不同类型的数据源（ValidExecuteModel vs WebSQLQueryResultSet）
 */
public interface ExportDataSourceStrategy {
    
    /**
     * 处理导出数据
     * 
     * @param request 导出请求参数
     * @param helper 数据传输助手
     * @throws Exception 处理过程中的异常
     */
    void processExportData(ExportDataRequest request, WebDataTransferHelper helper) throws Exception;
    
    /**
     * 判断当前策略是否适用于给定的请求
     * 
     * @param request 导出请求参数
     * @return true 如果适用，false 否则
     */
    boolean isApplicable(ExportDataRequest request);
}
