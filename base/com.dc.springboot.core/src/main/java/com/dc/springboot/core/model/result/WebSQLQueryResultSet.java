
package com.dc.springboot.core.model.result;

import com.dc.summer.model.data.DBDAttributeBinding;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * Web SQL query resultset.
 */
@Data
@ApiModel(value = "Web SQL 查询结果集")
public class WebSQLQueryResultSet {

    @ApiModelProperty(value = "列数据")
    private WebSQLQueryResultColumn[] columns;

    @ApiModelProperty(value = "原始列数据")
    private WebSQLQueryResultColumn[] originalColumns;

    @ApiModelProperty(value = "敏感列数据")
    private WebSQLQueryResultColumn[] sensitiveColumns;

    @ApiModelProperty(value = "行数据")
    private Object[][] rows;

    @ApiModelProperty(value = "原始行数据")
    private Object[][] originalRows;

    @ApiModelProperty(value = "单独的表实体")
    private boolean singleEntity = true;

    @ApiModelProperty(value = "具有行ID")
    private boolean hasRowIdentifier;

    @ApiModelProperty(value = "更新行计数")
    private Long updateRowCount;
    @ApiModelProperty(value = "结果ID - 用来更新结果集")
    private String resultId;

    @ApiModelProperty(value = "结果集索引 - 用来导出数据")
    private int index;

    @ApiModelProperty(value = "有更多数据")
    private boolean hasMoreData;

    @ApiModelProperty(value = "启用限制")
    private boolean isLimit; //select行数是否限制了

    @ApiModelProperty(value = "行数限制")
    private long rowsLimit;

    @ApiModelProperty(value = "本地导出行数限制")
    private boolean isExportLimit; //本地导出行数是否限制了

    @ApiModelProperty(value = "本地导出的最大行数限制")
    private long exportRowsLimit;

    @ApiModelProperty(value = "前端结果集每页最大行数")
    private long pageSize;

    /**
     * 此参数为导出行数，导出提示所用
     */
    private long exportRowsCount;

    @ApiModelProperty(value = "执行 SQL")
    private String sql;

    @ApiModelProperty(value = "具体的替换数据", example = "test.csv, 0, 0, 1")
    private List<Object> data = Collections.emptyList();

    @ApiModelProperty(value = "分页偏移量 - 用于执行导出，需要返回给前端")
    private long offset;

    @JsonIgnore
    private String resultName;

    public WebSQLQueryResultSet() {
    }

    public void setResultId(String resultId) {
        this.resultId = resultId;
    }

    public void setOriginalColumns(WebSQLQueryResultColumn[] originalColumns) {
        this.originalColumns = originalColumns;
    }

    public void setOriginalColumnsByBinding(DBDAttributeBinding[] bindings) {
        WebSQLQueryResultColumn[] originalColumns = new WebSQLQueryResultColumn[bindings.length];
        for (int i = 0; i < bindings.length; i++) {
            originalColumns[i] = new WebSQLQueryResultColumn(bindings[i]);
        }
        this.originalColumns = originalColumns;
    }

    public void setColumnsByBinding(DBDAttributeBinding[] bindings) {
        WebSQLQueryResultColumn[] columns = new WebSQLQueryResultColumn[bindings.length];
        for (int i = 0; i < bindings.length; i++) {
            columns[i] = new WebSQLQueryResultColumn(bindings[i]);
        }
        this.columns = columns;
    }

    public void setResultsInfo(WebSQLResultsInfo resultsInfo) {
        this.resultId = resultsInfo == null ? null : resultsInfo.getId();
    }

}
