
package com.dc.parser.ext.mysql.statement.ddl;

import com.dc.parser.ext.mysql.statement.MySQLStatement;
import com.dc.parser.model.statement.ddl.DropTableStatement;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * MySQL drop table statement.
 */
@RequiredArgsConstructor
@Getter
@Setter
public final class MySQLDropTableStatement extends DropTableStatement implements MySQLStatement {

    private boolean ifExists;
}
