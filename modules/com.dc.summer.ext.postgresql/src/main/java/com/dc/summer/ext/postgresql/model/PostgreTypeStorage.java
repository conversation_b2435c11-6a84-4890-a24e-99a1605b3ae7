
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.model.DBPNamedObject;

/**
 * PostgreTypeStorage
 */
public enum PostgreTypeStorage implements DBPNamedObject
{
    p("plain"),
    e("secondary"),
    m("compressed"),
    x("any");

    private final String desc;

    PostgreTypeStorage(String desc) {
        this.desc = desc;
    }

    @Override
    public String getName() {
        return desc;
    }
}
