package com.dc.parser.test.it.internal.asserts.statement.dcl.impl;

import com.dc.parser.ext.mssql.statement.dcl.SQLServerRevokeStatement;
import com.dc.parser.ext.mysql.statement.dcl.MySQLRevokeStatement;
import com.dc.parser.model.statement.dcl.RevokeStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.statement.dcl.impl.mysql.MySQLRevokeStatementAssert;
import com.dc.parser.test.it.internal.asserts.statement.dcl.impl.sqlserver.SQLServerRevokeStatementAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dcl.RevokeStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Revoke statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class RevokeStatementAssert {

    /**
     * Assert revoke statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual revoke statement
     * @param expected      expected revoke statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final RevokeStatement actual, final RevokeStatementTestCase expected) {
        if (actual instanceof MySQLRevokeStatement) {
            MySQLRevokeStatementAssert.assertIs(assertContext, (MySQLRevokeStatement) actual, expected);
        } else if (actual instanceof SQLServerRevokeStatement) {
            SQLServerRevokeStatementAssert.assertIs(assertContext, (SQLServerRevokeStatement) actual, expected);
        }
    }
}
