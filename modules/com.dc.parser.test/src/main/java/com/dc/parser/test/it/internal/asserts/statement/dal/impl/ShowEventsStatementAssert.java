package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.ShowEventsStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.database.DatabaseAssert;
import com.dc.parser.test.it.internal.asserts.segment.show.ShowFilterAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.ShowEventsStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Show events statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ShowEventsStatementAssert {

    /**
     * Assert show events statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual show events statement
     * @param expected      expected show events statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ShowEventsStatement actual, final ShowEventsStatementTestCase expected) {
        if (actual.getFromDatabase().isPresent()) {
            DatabaseAssert.assertIs(assertContext, actual.getFromDatabase().get().getDatabase(), expected.getFromDatabase().getDatabase());
            SQLSegmentAssert.assertIs(assertContext, actual.getFromDatabase().get(), expected.getFromDatabase());
        }
        if (actual.getFilter().isPresent()) {
            ShowFilterAssert.assertIs(assertContext, actual.getFilter().get(), expected.getFilter());
        }
    }
}
