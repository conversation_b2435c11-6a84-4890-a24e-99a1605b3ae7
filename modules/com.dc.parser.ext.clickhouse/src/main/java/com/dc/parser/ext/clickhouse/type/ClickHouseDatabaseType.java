
package com.dc.parser.ext.clickhouse.type;

import com.dc.infra.database.type.DatabaseType;

import java.util.Arrays;
import java.util.Collection;

/**
 * Database type of ClickHouse.
 */
public final class ClickHouseDatabaseType implements DatabaseType {
    
    @Override
    public Collection<String> getJdbcUrlPrefixes() {
        return Arrays.asList("jdbc:ch:", "jdbc:clickhouse:");
    }
    
    @Override
    public Constant getType() {
        return Constant.CLICKHOUSE;
    }
}
