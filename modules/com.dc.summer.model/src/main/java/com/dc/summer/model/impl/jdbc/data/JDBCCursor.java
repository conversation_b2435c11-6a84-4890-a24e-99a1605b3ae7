
package com.dc.summer.model.impl.jdbc.data;

import com.dc.summer.model.DBConstants;
import com.dc.code.Nullable;
import com.dc.summer.Log;
import com.dc.summer.model.data.DBDCursor;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.exec.JDBCResultSetImpl;
import com.dc.utils.CommonUtils;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Result set holder
 */
public class JDBCCursor implements DBDCursor {

    private static final Log log = Log.getLog(JDBCCursor.class);

    private JDBCSession session;
    private JDBCResultSet resultSet;
    private String cursorName;
    private boolean closeResultsOnRelease = true;

    public JDBCCursor(JDBCSession session, ResultSet resultSet, String description) throws SQLException {
        //super(session, null, original, description, true);
        this.session = session;
        this.resultSet = JDBCResultSetImpl.makeResultSet(session, null, resultSet, description, true);
    }

    @Override
    public Object getRawValue() {
        return resultSet;
    }

    @Override
    public boolean isNull() {
        return false;
    }

    @Override
    public boolean isModified() {
        return false;
    }

    @Override
    public void release() {
        if (resultSet != null) {
            if (closeResultsOnRelease) {
                try {
                    resultSet.close();
                } catch (Exception e) {
                    log.error(e);
                }
            }
            resultSet = null;
        }
    }

    @Override
    public DBCResultSet openResultSet(DBCSession session) {
        if (resultSet != null) {
            // Scroll to the beginning
            try {
                resultSet.absolute(0);
            } catch (SQLException e) {
                log.debug(e);
            }
        }
        return resultSet;
    }

    @Nullable
    @Override
    public String getCursorName() {
        return cursorName;
    }

    public void setCursorName(String cursorName) {
        this.cursorName = cursorName;
    }

    public void setCloseResultsOnRelease(boolean closeResultsOnRelease) {
        this.closeResultsOnRelease = closeResultsOnRelease;
    }

    @Override
    public String toString() {
        if (cursorName != null) {
            return cursorName;
        }
        if (resultSet == null) {
            return DBConstants.NULL_VALUE_LABEL;
        }
        return CommonUtils.toString(resultSet.getSourceStatement().getQueryString());
    }

}
