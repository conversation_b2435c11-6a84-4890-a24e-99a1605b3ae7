package com.dc.mockdata.engine.generator.advanced.finnegan;

import com.dc.mockdata.engine.generator.advanced.AdvancedStringValueGenerator;
import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSDataManipulator;
import com.dc.utils.CommonUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Locale;
import java.util.Map;

public class FinneganTextGenerator extends AdvancedStringValueGenerator {

    private Finnegan finnegan;
    private String schema;
    private int minWordCount;
    private int maxWordCount;
    private long startSeed;

    public FinneganTextGenerator() {
    }

    @Override
    public void init(DBSDataManipulator container, DBSAttributeBase attribute, Map<String, Object> properties) throws DBException {
        super.init(container, attribute, properties);
        this.schema = CommonUtils.toString(properties.get("schema"));
        this.minWordCount = CommonUtils.toInt(properties.get("minWordCount"), 5);
        this.maxWordCount = CommonUtils.toInt(properties.get("maxWordCount"), 10);
        this.startSeed = System.currentTimeMillis();

        try {
            Field field = Finnegan.class.getField(this.schema.toUpperCase(Locale.ENGLISH));
            this.finnegan = (Finnegan) field.get(null);
        } catch (Throwable var5) {
            throw new DBException("Bad schema: " + this.schema, var5);
        }
    }

    @Override
    protected Object generateOneValue(DBRProgressMonitor monitor) throws DBException, IOException {
        return this.finnegan
                .sentence(
                        this.startSeed++, this.minWordCount, this.maxWordCount, new String[]{",", ",", ",", ";"}, new String[]{".", ".", ".", "!", "?", "..."}, 0.17
                );
    }
}
