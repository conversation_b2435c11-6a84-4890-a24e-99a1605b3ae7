
package com.dc.summer.ext.mssql.edit.generic;

import com.dc.code.NotNull;
import com.dc.summer.ext.generic.edit.GenericTableManager;
import com.dc.summer.ext.generic.model.GenericTableForeignKey;
import com.dc.summer.ext.generic.model.GenericTableIndex;
import com.dc.summer.ext.generic.model.GenericUniqueKey;
import com.dc.summer.ext.mssql.model.generic.SQLServerGenericTableColumn;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

/**
 * SQLServerGenericTableManager
 */
public class SQLServerGenericTableManager extends GenericTableManager {

    private static final Class<? extends DBSObject>[] CHILD_TYPES = CommonUtils.array(
        SQLServerGenericTableColumn.class,
        GenericUniqueKey.class,
        GenericTableForeignKey.class,
        GenericTableIndex.class
    );

    @NotNull
    @Override
    public Class<? extends DBSObject>[] getChildTypes() {
        return CHILD_TYPES;
    }

}
